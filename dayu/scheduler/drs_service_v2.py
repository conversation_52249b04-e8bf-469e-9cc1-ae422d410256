import json
import time
import uuid

import pandas as pd
from collections import deque
from contextlib import contextmanager
from itertools import combinations
from nameko.extensions import DependencyProvider
from nameko.timer import timer
# from nameko.rpc import rpc
from nameko.web.handlers import http
from sqlalchemy import create_engine
from sqlalchemy.exc import OperationalError
from sqlalchemy.orm import sessionmaker
from werkzeug.wrappers import Response

import settings
from api.openstack.client import Client
from api.prometheus.client import Client as PClient
from dayu.scheduler import service
from db.model.cluster import ClusterHost
from db.model.host_drs import ClusterDrs


# 单位是秒
CPU_weight_value_host = 0.5
MEM_weight_value_host = 0.5
CPU_weight_value_vm = 0.5
MEM_weight_value_vm = 0.5


class DB:
    db_url  = 'mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8'.format(
        settings.DB_USERNAME,
        settings.DB_PASSWORD,
        settings.DB_HOSTNAME,
        settings.DB_PORT,
        settings.DB_DATABASE)
    engine  = create_engine(
        db_url,
        pool_size=50,
        max_overflow=20,
        pool_recycle=3600,
        pool_timeout=10,
        pool_pre_ping=True)
    Session = sessionmaker(bind=engine)

    @classmethod
    @contextmanager
    def session_scope(self, retries=3, delay=5):
        session  = self.Session()
        attempts = 0

        while attempts < retries:
            try:
                yield session
                session.commit()
                break
            except OperationalError as e:
                session.rollback()
                attempts += 1
                print("Connection failed, retrying {}/{} ...".format(attempts, retries))
                if attempts >= retries:
                    print("The connection attempt failed, please check the database server.")
                    raise e
                time.sleep(delay)
            except Exception as e:
                session.rollback()
                print("Execution failed: {}".format(e))
                raise
            finally:
                session.close()

class DrsServiceV2:
    name = "drs_service_v2"
    
    # 添加全局DataFrame存储
    cluster_df = None  # 集群主机信息
    vm_df = None      # 虚拟机负载状态
    drs_df = None     # 主机DRS配置
    host_df = None # host_diffDataFrame
    last_update_time = None
    recommended_response_data = {}

    # recommended_data_df = None
    dispatch_execution_df = None

    # 添加策略字典为类属性
    strategies = {
        "conservative": [70, 30],
        "neutral": [70, 20],
        "radical": [50, 10], #测试用
        "most_radical": [50, 5] #测试用
    }

    # 添加黑名单字典，key是vm_name，value是加入时间戳
    blacklist = {}
    snapshot_html = None  # 添加新的类属性用于存储快照

    # 添加迁移冷却相关属性

    last_migration_time = None  # 记录最后一次迁移时间

    def __init__(self):
        self.data_average_load = {}

    def add_to_blacklist(self, vm_name):
        """添加虚拟机到黑名单"""
        self.blacklist[vm_name] = time.time()

    def is_in_blacklist(self, vm_name):
        """检查虚拟机是否在黑名单中"""
        if vm_name not in self.blacklist:
            return False
        # 检查是否超过20分钟
        if time.time() - self.blacklist[vm_name] > service.BLACKLIST_TIMEOUT:
            self.blacklist.pop(vm_name)
            return False
        return True

    def clean_blacklist(self):
        """定期清理黑名单中超时的条目"""
        current_time = time.time()
        to_remove = []
        for vm_name, add_time in self.blacklist.items():
            if current_time - add_time > service.BLACKLIST_TIMEOUT:
                to_remove.append(vm_name)
        for vm_name in to_remove:
            self.blacklist.pop(vm_name)

    def get_recommended(self):
        """返回推荐的主机迁移信息"""
        try:
            if self.is_auto_drs_enabled():
                return Response                                                                                                                                                                                                                                                                                                                                                                (
                    json.dumps({"msg": "自动DRS已启用"}),
                    status=200,
                    headers={
                        "Content-Type": "application/json",
                        "Cache-Control": "no-store, no-cache, must-revalidate, max-age=0",
                        "Pragma": "no-cache",
                        "Expires": "0"
                    }
                )

            # 检查是否有正在进行的迁移
            if (self.dispatch_execution_df is not None and 
                not self.dispatch_execution_df.empty and 
                (self.dispatch_execution_df['status'] != "准备").any()):
                return Response(
                    json.dumps({"msg": "已有迁移任务正在执行"}),
                    status=200,
                    headers={
                        "Content-Type": "application/json",
                        "Cache-Control": "no-store, no-cache, must-revalidate, max-age=0",
                        "Pragma": "no-cache",
                        "Expires": "0"
                    }
                )

            # 构造响应数据
            if self.recommended_response_data == {}:
                self.recommended_response_data = {
                    "msg": "没有推荐数据"
                }
            response_data =self.recommended_response_data
            print("response_data:",response_data)

            return Response(
                json.dumps(response_data, ensure_ascii=False),
                status=200,
                headers={
                    "Content-Type": "application/json",
                    "Cache-Control": "no-store, no-cache, must-revalidate, max-age=0",
                    "Pragma": "no-cache",
                    "Expires": "0"
                }
            )

        except Exception as e:
            error_data = {
                "msg": "获取推荐数据失败",
                "detail": str(e),
            }
        return Response(
            json.dumps(error_data),
            status=500,
            headers={
                "Content-Type": "application/json",
                "Cache-Control": "no-store, no-cache, must-revalidate, max-age=0",
                "Pragma": "no-cache",
                "Expires": "0"
            }
        )

    def session_scope(self):
        return DB.session_scope()

    def update_cluster_info(self):
        """更新集群主机信息定时器"""
        print("更新集群主机信息...")
        try:
            df_data = {
                'hostname': [],
                'ip': [],
                'enabled_auto': [],
                'cpu_average_load': [],
                'mem_average_load': [],
                'cpu_cores': [],      # 新增CPU核数字段
                'mem_total_gb': [],   # 新增内存总量字段(GB)
                'mem_use_gb': [],   # 新增内存使用量字段(GB)
                '权重值': []
            }
            
            with self.session_scope() as session:
                cluster_hosts = session.query(ClusterHost).all()
                for host in cluster_hosts:
                    # 从Prometheus获取主机负载数据
                    cpu_usage = self._get_host_cpu_usage(host.hostname)
                    mem_usage = self._get_host_mem_usage(host.hostname)
                    cpu_cores = self._get_host_cpu_cores(host.hostname)  # 获取CPU核数
                    mem_total = self._get_host_mem_total(host.ip)  # 获取内存总量
                    mem_used = mem_total * mem_usage / 100 # 计算内存使用量

                    df_data['hostname'].append(host.hostname)
                    df_data['ip'].append(host.ip)
                    df_data['enabled_auto'].append(host.enabled_auto)
                    df_data['cpu_average_load'].append(cpu_usage)
                    df_data['mem_average_load'].append(mem_usage)
                    df_data['cpu_cores'].append(cpu_cores)
                    df_data['mem_total_gb'].append(mem_total)
                    df_data['mem_use_gb'].append(float('%.3f' % mem_used))
                    df_data['权重值'].append(float('%.3f' % (cpu_usage + mem_usage)))

            self.cluster_df = pd.DataFrame(df_data)
            self.cluster_df = self.cluster_df.sort_values('权重值', ascending=False)
            self.last_update_time = time.time()
            
            print(f"更新集群主机信息完成： {time.strftime('%Y-%m-%d %H:%M:%S')}")
        except Exception as e:
            print(f"更新集群主机信息错误: {str(e)}")

    def update_vm_info(self):
        """更新虚拟机负载状态定时器"""
        print("更新虚拟机负载...")
        try:
            if self.host_df is None or self.host_df.empty:
                print("Not host_df info")
                return

            active_config = self.host_df.iloc[0]
            mem_date = active_config['可用内存大小']
            cpu_date = active_config['可用CPU使用率']
            cpu_rate = active_config['CPU核心数差异比']

            vm_data = []
            client = Client()
            
            with self.session_scope() as session:
                cluster_hosts = session.query(ClusterHost).all()
                for host in cluster_hosts:
                    try:
                        # 获取主机上的VM列表
                        servers = self.openstack_sorted_servers_in_host(host.hostname, host.ip)
                        for server in servers:
                            # 获取VM的资源使用情况
                            vm_data.append({
                                'id': server.get('id', ''),
                                'vm_name': server.get('name', ''),
                                'task_state': server.get('task_state', ''),
                                'host': host.hostname,
                                'cpu_usage_for_vm': float('%.3f' % server.get('cpu_used_percent_vm', 0)),
                                'mem_used_rate_vm': float('%.3f' % server.get('mem_used_rate', 0)),
                                'mem_used_bite_gb': float('%.3f' % server.get('mem_used_bite', 0)),
                                'mem_usage_for_host': float('%.3f' % server.get('mem_used_percent', 0)),
                                'cpu_usage_for_host': float('%.3f' % server.get('cpu_used_percent', 0)),
                                '是否是黑名单': self.is_in_blacklist(server.get('name', '')),  # 检查是否在黑名单中
                                '是否满足调入主机可用内存': mem_date - server.get('mem_used_bite', 0) > 0,
                                '是否满足调入主机可用CPU使用率': cpu_date - server.get('cpu_used_percent') * cpu_rate > 0,
                                '综合值': float('%.3f' % (server.get('mem_used_percent', 0) + server.get('cpu_used_percent', 0))) *
                                          (1 if (
                                                  not self.is_in_blacklist(server.get('name', '')) and  # 加入黑名单检查
                                                  mem_date - server.get('mem_used_bite', 0) > 0 and
                                                  cpu_date - server.get('cpu_used_percent') * cpu_rate > 0
                                          ) else -1)
                            })
                    except Exception as e:
                        print(f"获取VM资源使用情况错误: {host.hostname}: {str(e)}")
                        continue

            # 更新DataFrame
            self.vm_df = pd.DataFrame(vm_data)
            if not self.vm_df.empty:
                self.vm_df = self.vm_df.sort_values(by=['综合值'], ascending=[False])

            print(f"更新虚拟机负载完成： {time.strftime('%Y-%m-%d %H:%M:%S')}")
        except Exception as e:
            print(f"更新虚拟机负载错误: {str(e)}")

    def update_drs_config(self):
        """更新主机DRS配置定时器"""
        print("更新主机DRS配置...")
        try:
            with self.session_scope() as session:
                drs_configs = session.query(ClusterDrs).all()
                data = []
                for config in drs_configs:
                    config_dict = config.__dict__.copy()
                    config_dict.pop('_sa_instance_state', None)
                    
                    # 根据策略添加migration_threshold和host_diff
                    strategy = config_dict.get('strategy', 'neutral')
                    if strategy in self.strategies:
                        migration_threshold, host_diff = self.strategies[strategy]
                    else:
                        # 如果策略不存在，使用neutral作为默认值
                        migration_threshold, host_diff = self.strategies['neutral']
                    
                    # 添加新列migration_threshold   host_diff
                    config_dict['migration_threshold'] = migration_threshold
                    config_dict['host_diff'] = host_diff
                    if config_dict.get("mem_enabled") == "true":
                        config_dict['mem_enabled'] = True
                    else:
                        config_dict['mem_enabled'] = False
                    if config_dict.get("cpu_enabled") == "true":
                        config_dict['cpu_enabled'] = True
                    else:
                        config_dict['cpu_enabled'] = False
                    
                    data.append(config_dict)
                
                self.drs_df = pd.DataFrame(data)
                print(f"更新DRS配置完成： {time.strftime('%Y-%m-%d %H:%M:%S')}")

        except Exception as e:
            print(f"更新DRS配置错误: {str(e)}")

    def update_resource_diff(self):
        """更新资源差值数据定时器 - 为每个主机生成与其他主机的资源差值表"""
        print("更新资源差值数据...")
        try:
            if self.drs_df is None or self.drs_df.empty:
                print("Not drs_df config")
                return
            active_config = self.drs_df[self.drs_df['enabled'] == "true"].iloc[0]
            mem_enabled = active_config['mem_enabled']
            cpu_enabled = active_config['cpu_enabled']

            migration_threshold = active_config['migration_threshold']
            required_host_diff = active_config['host_diff']

            if self.cluster_df is None or len(self.cluster_df) < 2:
                print("没有足够的主机数据")
                return

            hosts = self.cluster_df['hostname'].tolist()
            host_diffs = {}  # 存储每个主机的差值表

            # 为每个主机创建一个差值表
            host = hosts[0]
            # 获取当前主机的数据
            host_data = self.cluster_df[self.cluster_df['hostname'] == host].iloc[0]

            # 准备其他主机的差值数据
            other_hosts = []
            other_host_cpus = []
            other_host_mems = []
            # 剩余内存
            other_host_mem_remains = []
            cpu_diffs = []
            mem_diffs = []
            available_mems = []
            available_cpus = []
            diff_cpu_cores = []
            meets = []
            total = []

            # 与其他所有主机计算差值
            for other_host in hosts:
                if other_host != host:
                    other_host_data = self.cluster_df[self.cluster_df['hostname'] == other_host].iloc[0]

                    # 计算差值
                    other_host_cpu = other_host_data['cpu_average_load']
                    other_host_mem = other_host_data['mem_average_load']
                    other_host_mem_remain = other_host_data['mem_total_gb'] - other_host_data['mem_use_gb']
                    cpu_diff = abs(host_data['cpu_average_load'] - other_host_data['cpu_average_load'])
                    mem_diff = abs(host_data['mem_average_load'] - other_host_data['mem_average_load'])
                    available_mem = other_host_data['mem_total_gb'] * migration_threshold / 100 - other_host_data['mem_use_gb']
                    available_cpu = 1 * migration_threshold  -  other_host_data['cpu_average_load']
                    diff_cpu_core = host_data['cpu_cores'] / other_host_data['cpu_cores']

                    # 新增条件判断逻辑
                    meet_condition = False
                    if cpu_enabled and mem_enabled:
                        meet_condition = (cpu_diff >= required_host_diff) and (mem_diff >= required_host_diff)
                    elif cpu_enabled:
                        meet_condition = cpu_diff >= required_host_diff
                    elif mem_enabled:
                        meet_condition = mem_diff >= required_host_diff
                    meet = meet_condition  # 这里赋值给meet变量

                    if meet_condition:
                        meet_value = 1
                    else:
                        meet_value = -1

                    other_hosts.append(other_host)
                    other_host_cpus.append(float('%.3f' % other_host_cpu))
                    other_host_mems.append(float('%.3f' % other_host_mem))
                    other_host_mem_remains.append(float('%.3f' % other_host_mem_remain))
                    cpu_diffs.append(float('%.3f' % cpu_diff))
                    mem_diffs.append(float('%.3f' % mem_diff))
                    available_mems.append(float('%.3f' % available_mem))
                    available_cpus.append(float('%.3f' % available_cpu))
                    meets.append(meet)
                    diff_cpu_cores.append(float('%.3f' % diff_cpu_core))
                    total.append(float('%.3f' % (other_host_cpu * CPU_weight_value_host + other_host_mem * MEM_weight_value_host)) * meet_value)

                # 创建该主机的差值DataFrame
                diff_data = {
                    '对比主机': other_hosts,
                    'CPU使用率': other_host_cpus,
                    '内存使用率': other_host_mems,
                    '剩余内存': other_host_mem_remains,
                    'CPU差值': cpu_diffs,
                    '内存差值': mem_diffs,
                    '是否满足条件':meets,
                    'CPU核心数差异比':diff_cpu_cores,
                    '可用内存大小':available_mems,
                    '可用CPU使用率':available_cpus,
                    '综合值': total
                }
                self.host_df = pd.DataFrame(diff_data)
                self.host_df = self.host_df.sort_values('综合值', ascending=True)
            
            print(f"更新资源差值数据完成：{time.strftime('%Y-%m-%d %H:%M:%S')}")

        except Exception as e:
            print(f"更新资源差值数据错误: {str(e)}")

    def _get_host_cpu_usage(self, hostname):
        """从Prometheus获取主机CPU使用率"""
        try:
            client = PClient()
            params = self.prometheus_time_interval()
            result = client.HostClient.get_host_cpu_usage(client, *params)
            
            for row in result["data"]["result"]:
                if row["metric"]["hypervisor_hostname"] == hostname:
                    values = [float(value[1]) for value in row["values"]]
                    return float('%.3f' % (sum(values) / len(values)))
            return 0.0
        except Exception:
            return 0.0

    def _get_host_mem_usage(self, hostname):
        """从Prometheus获取主机内存使用率"""
        try:
            client = PClient()
            params = self.prometheus_time_interval()
            result = client.HostClient.get_host_mem_usage(client, *params)
            
            for row in result["data"]["result"]:
                if row["metric"]["hypervisor_hostname"] == hostname:
                    values = [float(value[1]) for value in row["values"]]
                    return float('%.3f' % (sum(values) / len(values)))
            return 0.0
        except Exception:
            return 0.0

    def _get_vm_cpu_usage(self, hostname, vm_name):
        """从Prometheus获取VM的CPU使用率"""
        try:
            client = PClient()
            params = self.prometheus_time_interval()
            result = client.HostClient.get_libvirt_domain_cpu_used_percent(client, *params, hostname, vm_name)
            
            if result["data"]["result"]:
                values = [float(value[1]) for value in result["data"]["result"][0]["values"]]
                return float('%.3f' % (sum(values) / len(values)))
            return 0.0
        except Exception:
            return 0.0

    def _get_vm_mem_usage(self, hostname, vm_name):
        """从Prometheus获取VM的内存使用率"""
        try:
            client = PClient()
            params = self.prometheus_time_interval()
            result = client.HostClient.libvirt_domain_memory_stats_usable_bytes(client, *params, hostname)
            
            for row in result["data"]["result"]:
                if row["metric"]["domain"] == vm_name:
                    values = [float(value[1]) for value in row["values"]]
                    return float('%.3f' % (sum(values) / len(values)))
            return 0.0
        except Exception:
            return 0.0

    def _get_host_cpu_cores(self, hostname):
        """从Prometheus获取主机CPU核数"""
        try:
            client = PClient()
            # 使用node_cpu_count指标获取CPU核数
            result = client.HostClient.get_libvirt_domain_cpu_core_count(client, hostname)
            if result:
                return result
            return 0
        except Exception as e:
            print(f"Error getting CPU cores for {hostname}: {str(e)}")
            return 0

    def _get_host_mem_total(self, addr):
        """从Prometheus获取主机总内存(GB)"""
        try:
            client = PClient()
            # 使用node_memory_MemTotal_bytes指标获取总内存
            result = client.HostClient.get_libvirt_domain_mem_total(client, addr)
            if result:
                # 转换为GB并保留2位小数
                mem_gb = float(result) / (1024 * 1024 * 1024)
                return round(mem_gb, 2)
            return 0.0
        except Exception as e:
            print(f"Error getting memory total for {addr}: {str(e)}")
            return 0.0

    def check_cluster_enabled(self):
        """检查集群是否启用"""
        try:
            cluster_status = []
            cluster_drs_status = False

            with self.session_scope() as session:
                # 检查所有主机的enabled_auto状态
                cluster_hosts = session.query(ClusterHost).all()
                for host in cluster_hosts:
                    enabled_auto = host.enabled_auto == "on"
                    cluster_status.append(enabled_auto)

                # 检查DRS配置的enabled状态
                host_drs = session.query(ClusterDrs).order_by(ClusterDrs.id).first()
                if host_drs and host_drs.enabled == "true":
                    cluster_drs_status = True

            # 只有当所有主机都启用且DRS配置也启用时，才返回True
            return bool(cluster_status and all(cluster_status) and cluster_drs_status)

        except Exception as e:
            print(f"Error checking cluster status: {str(e)}")
            return False

    def update_recommended_data_df(self):
        """更新推荐数据"""
        try:
            # 检查是否在冷却时间内
            if (self.last_migration_time is not None and 
                time.time() - self.last_migration_time < service.MIGRATION_COOLDOWN):
                print(f"迁移冷却中，剩余 {service.MIGRATION_COOLDOWN - (time.time() - self.last_migration_time):.0f} 秒")
                return

            # 清理已完成或超时的记录
            if self.dispatch_execution_df is not None and not self.dispatch_execution_df.empty:
                self.dispatch_execution_df = self.dispatch_execution_df[
                    ~self.dispatch_execution_df['status'].isin(["已完成", "已超时"])
                ]
                # 如果清理后DataFrame为空，则重置为None并清空响应数据
                if self.dispatch_execution_df.empty:
                    self.dispatch_execution_df = None
                    self.recommended_response_data = {}
                    self.snapshot_html = None

            # 如果当前有正在执行的迁移任务，则不更新
            if (self.dispatch_execution_df is not None and 
                not self.dispatch_execution_df.empty and 
                (self.dispatch_execution_df['status'] != "准备").any()):
                return

            if self.cluster_df is None or self.cluster_df.empty:
                return

            host_info = self.cluster_df.iloc[0].to_dict()

            if self.drs_df is None or self.drs_df.empty:
                return

            recommended_drs = self.drs_df.iloc[0].to_dict()

            if recommended_drs.get('cpu_enabled'):
                if host_info.get('cpu_average_load', 0) < recommended_drs.get('migration_threshold'):
                    return
            if recommended_drs.get('mem_enabled'):
                if host_info.get('mem_average_load', 0) < recommended_drs.get('migration_threshold'):
                    return
            if recommended_drs.get('cpu_enabled') and recommended_drs.get('mem_enabled'):
                if host_info.get('cpu_average_load', 0) < recommended_drs.get('migration_threshold') and host_info.get('mem_average_load', 0) < recommended_drs.get('migration_threshold'):
                    return
            if not (recommended_drs.get('cpu_enabled') and not recommended_drs.get('mem_enabled')):
                return

            # 检查数据是否准备好
            if self.vm_df is None or self.vm_df.empty:
                return

            if self.host_df is None or self.host_df.empty:
                return

            recommended_hosts = self.host_df[self.host_df['是否满足条件'] == True]['对比主机'].tolist()

            # 获取推荐的虚拟机数据
            host_vms = self.vm_df[self.vm_df['host'] == host_info['hostname']]
            if host_vms.empty:
                print(f"主机 {host_info['hostname']} 没有可迁移的虚拟机")
                # 清理状态为"准备"的记录
                if self.dispatch_execution_df is not None and not self.dispatch_execution_df.empty:
                    self.dispatch_execution_df = self.dispatch_execution_df[
                        self.dispatch_execution_df['status'] != "准备"
                        ]
                    if self.dispatch_execution_df.empty:
                        self.dispatch_execution_df = None
                        self.recommended_response_data = {}
                        self.snapshot_html = None
                return
            recommended_vm = host_vms.iloc[0].to_dict()
            # 创建新的DataFrame
            data = []
            vm_dict = {
                'vm_id': recommended_vm['id'],
                'vm_name': recommended_vm['vm_name'],
                'host': recommended_vm['host'],
                'recommended_host': recommended_hosts[0] if recommended_hosts else None,
                'status': "准备",
                'start_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'created_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'updated_time': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            data.append(vm_dict)
            
            # 只有当没有数据或者现有数据状态为"准备"时才更新
            if (self.dispatch_execution_df is None or 
                self.dispatch_execution_df.empty or 
                (self.dispatch_execution_df['status'] == "准备").all()):
                self.dispatch_execution_df = pd.DataFrame(data)
                # 生成并保存快照
                self.snapshot_html = self.generate_snapshot_html()

                current_timestamp = time.time()
                expiry_timestamp = current_timestamp + service.DRS_RECOMMENDED_INTERVAL

                # 更新响应数据
                self.recommended_response_data = {
                    "msg": "ok",
                    "data": {
                        "vm": recommended_vm,
                        "host": host_info,
                        "drs": recommended_drs,
                        "recommended_hosts": recommended_hosts,
                        "expiry_date": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(expiry_timestamp))
                    }
                }
            
        except Exception as e:
            print(f"更新推荐数据错误: {str(e)}")
            # 发生错误时也清空响应数据
            self.recommended_response_data = {}
            self.snapshot_html = None

    def generate_snapshot_html(self):
        """生成数据快照的HTML"""
        try:
            tables_html = []
            
            if self.cluster_df is not None and not self.cluster_df.empty:
                tables_html.append("""
                    <div class="section">
                        <h3>快照时刻集群主机信息表</h3>
                        {}
                    </div>
                """.format(self.cluster_df.to_html(classes='table')))

            if self.host_df is not None and not self.host_df.empty:
                tables_html.append(f"""
                    <div class="section">
                        <h3>快照时刻资源差值表</h3>
                        {self.host_df.to_html(classes='table')}
                    </div>
                """)
            
            if self.vm_df is not None and not self.vm_df.empty:
                tables_html.append("""
                    <div class="section">
                        <h3>快照时刻虚拟机负载状态表</h3>
                        {}
                    </div>
                """.format(self.vm_df.to_html(classes='table')))
            
            if self.drs_df is not None and not self.drs_df.empty:
                tables_html.append("""
                    <div class="section">
                        <h3>快照时刻DRS配置表</h3>
                        {}
                    </div>
                """.format(self.drs_df.to_html(classes='table')))

            html_content = f"""
            <!DOCTYPE html>
            <html lang="zh">
            <head>
                <meta charset="UTF-8">
                <title>迁移决策时刻的数据快照</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .section {{ margin-bottom: 30px; }}
                    table {{ 
                        border-collapse: collapse; 
                        width: 100%; 
                        margin-bottom: 20px;
                        box-shadow: 0 1px 3px rgba(0,0,0,0.2);
                    }}
                    th, td {{ 
                        border: 1px solid #ddd; 
                        padding: 12px 8px;
                        text-align: left;
                    }}
                    th {{ 
                        background-color: #f5f5f5;
                        font-weight: bold;
                    }}
                    tr:nth-child(even) {{ background-color: #f9f9f9; }}
                    tr:hover {{ background-color: #f5f5f5; }}
                    h2, h3 {{ color: #333; }}
                </style>
            </head>
            <body>
                <h2>迁移决策时刻的数据快照</h2>
                <p><b>快照时间：</b>{time.strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p><b>UUID：</b>{str(uuid.uuid4())}</p>
                {''.join(tables_html)}
            </body>
            </html>
            """
            return html_content
        except Exception as e:
            print(f"生成快照HTML错误: {str(e)}")
            return None

    def get_all_tables(self):
        """展示所有DataFrame数据的接口"""
        try:
            # 准备所有表格数据
            tables_html = []
            
            # 1. 集群主机信息表
            if self.cluster_df is not None and not self.cluster_df.empty:
                tables_html.append("""
                    <div class="section">
                        <h3>集群主机信息表</h3>
                        {}
                    </div>
                """.format(self.cluster_df.to_html(classes='table')))

            # 2. 添加每个主机的资源差值表
            if self.host_df is not None and not self.host_df.empty:
                tables_html.append(f"""
                    <div class="section">
                        <h3>最高负载机器与其他机器的资源差值表</h3>
                        {self.host_df.to_html(classes='table')}
                    </div>
                """)
            
            # 3. 虚拟机负载状态表
            if self.vm_df is not None and not self.vm_df.empty:
                tables_html.append("""
                    <div class="section">
                        <h3>虚拟机负载状态表</h3>
                        {}
                    </div>
                """.format(self.vm_df.to_html(classes='table')))

            # # 4. 虚拟机推荐表
            # if self.recommended_data_df is not None and not self.recommended_data_df.empty:
            #     tables_html.append("""
            #         <div class="section">
            #             <h3>虚拟机推荐表</h3>
            #             {}
            #         </div>
            #     """.format(self.recommended_data_df.to_html(classes='table')))

            # 5. 调度执行表
            if self.dispatch_execution_df is not None and not self.dispatch_execution_df.empty:
                tables_html.append("""
                    <div class="section">
                        <h3>调度执行表</h3>
                        {}
                    </div>
                """.format(self.dispatch_execution_df.to_html(classes='table')))
            
            # 6. 主机DRS配置表
            if self.drs_df is not None and not self.drs_df.empty:
                tables_html.append("""
                    <div class="section">
                        <h3>主机DRS配置表</h3>
                        {}
                    </div>
                """.format(self.drs_df.to_html(classes='table')))


            # <meta http-equiv="refresh" content="10">
            # 生成完整的HTML页面
            html_content = f"""
            <!DOCTYPE html>
            <html lang="zh">
            <head>
                <meta charset="UTF-8">
                <title>DRS资源调度系统 - 全部数据表</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .section {{ margin-bottom: 30px; }}
                    table {{ 
                        border-collapse: collapse; 
                        width: 100%; 
                        margin-bottom: 20px;
                        box-shadow: 0 1px 3px rgba(0,0,0,0.2);
                    }}
                    th, td {{ 
                        border: 1px solid #ddd; 
                        padding: 12px 8px;
                        text-align: left;
                    }}
                    th {{ 
                        background-color: #f5f5f5;
                        font-weight: bold;
                    }}
                    tr:nth-child(even) {{ background-color: #f9f9f9; }}
                    tr:hover {{ background-color: #f5f5f5; }}
                    h2, h3 {{ color: #333; }}
                    .status {{ 
                        padding: 10px; 
                        margin: 10px 0;
                        background-color: #f8f9fa;
                        border-radius: 4px;
                    }}
                </style>
            </head>
            <body>
                <h2>DRS资源调度系统 - 全部数据表</h2>
                <div class="status">
                    <p><b>更新时间：</b>{time.strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
                {''.join(tables_html)}
            </body>
            </html>
            """
# <p><b>集群状态：</b>{'已开启' if self.check_cluster_enabled() else '未开启'}</p>
            return Response(
                html_content,
                status=200,
                headers={"Content-Type": "text/html; charset=utf-8"}
            )

        except Exception as e:
            error_html = f"""
                <h1>Error</h1>
                <p>获取数据表时发生错误: {str(e)}</p>
            """
            return Response(
                error_html,
                status=500,
                headers={"Content-Type": "text/html; charset=utf-8"}
            )

    def drs_migrate(self, request):
        """V2版本的虚拟机迁移接口"""
        try:
            data = json.loads(request.get_data(as_text=True))
            print("收到迁移请求:", data)

            # 必要的参数验证
            required_fields = ['id', 'vm_name', 'target_host']
            if not all(field in data for field in required_fields):
                return Response(
                    json.dumps({
                        "msg": "err",
                        "data": "缺少必要参数, 需要: vm_id, vm_name, target_host"
                    }),
                    status=400,
                    headers={"Content-Type": "application/json"}
                )

            vm_id = data['id']
            vm_name = data['vm_name']
            target_host = data['target_host']

            # 调用openstack客户端执行迁移
            client = Client()
            migration_data = {
                "id": vm_id,
                "action": "live_migrate",
                "data": target_host
            }
            print(f"执行迁移: VM {vm_name}({vm_id}) -> {target_host}")
            
            # 更新调度执行表的开始时间和状态
            if self.dispatch_execution_df is not None and not self.dispatch_execution_df.empty:
                self.dispatch_execution_df.loc[
                    self.dispatch_execution_df['vm_name'] == vm_name,
                    ['start_time', 'status', 'updated_time']
                ] = [time.strftime('%Y-%m-%d %H:%M:%S'), "开始", time.strftime('%Y-%m-%d %H:%M:%S')]
            
            migration_result = client.NovaClient.openstack_server_action_v2(client, migration_data)

            if migration_result.get('msg') == 'ok':
                # 确保时间戳正确格式化
                current_time = time.strftime('%Y-%m-%d %H:%M:%S')
                
                # 更新调度执行表状态
                if self.dispatch_execution_df is not None and not self.dispatch_execution_df.empty:
                    mask = self.dispatch_execution_df['vm_name'] == vm_name
                    if any(mask):
                        self.dispatch_execution_df.loc[mask, 'status'] = "已发送"
                        self.dispatch_execution_df.loc[mask, 'start_time'] = current_time
                        self.dispatch_execution_df.loc[mask, 'updated_time'] = current_time
                        print(f"已更新迁移状态为已发送: {vm_name}")
                
                return Response(
                    json.dumps({
                        "msg": "ok",
                        "snapshot_html": self.snapshot_html
                    }),
                    status=200,
                    headers={"Content-Type": "application/json"}
                )
            else:
                current_time = time.strftime('%Y-%m-%d %H:%M:%S')
                
                # 更新状态为已超时
                if self.dispatch_execution_df is not None and not self.dispatch_execution_df.empty:
                    mask = self.dispatch_execution_df['vm_name'] == vm_name
                    if any(mask):
                        self.dispatch_execution_df.loc[mask, 'status'] = "已超时"
                        self.dispatch_execution_df.loc[mask, 'updated_time'] = current_time
                        # 确保加入黑名单
                        self.add_to_blacklist(vm_name)
                        print(f"已将 {vm_name} 添加到黑名单")
                
                return Response(
                    json.dumps({
                       "msg": "迁移失败",
                        "snapshot_html": self.snapshot_html
                    }),
                    status=500, 
                    headers={"Content-Type": "application/json"}
                )

        except Exception as e:
            print(f"迁移过程发生错误: {str(e)}")
            # 更新状态为已超时并加入黑名单
            if self.dispatch_execution_df is not None and not self.dispatch_execution_df.empty:
                self.dispatch_execution_df.loc[
                    self.dispatch_execution_df['vm_name'] == vm_name,
                    ['status', 'updated_time']
                ] = ["已超时", time.strftime('%Y-%m-%d %H:%M:%S')]
                self.add_to_blacklist(vm_name)  # 添加到黑名单
            return Response(
                json.dumps({
                    "msg": "迁移失败",
                    "snapshot_html": self.snapshot_html
                }),
                status=500,
                headers={"Content-Type": "application/json"}
            )

    def check_migration_status(self):
        """定时检查迁移状态"""
        if self.dispatch_execution_df is None or self.dispatch_execution_df.empty:
            return
        
        current_time = time.strftime('%Y-%m-%d %H:%M:%S')
        deleted_vm_indices = []
        
        # 检查所有记录，不仅仅是已发送的
        for index, row in self.dispatch_execution_df.iterrows():
            vm_name = row['vm_name']
            vm_id = row['vm_id']
            
            # 检查虚拟机是否存在
            try:
                vm_info_df = self.get_vm_info_df(vm_id)
                
                # 如果虚拟机不存在或信息为空，认为虚拟机已被删除
                if vm_info_df is None or vm_info_df.empty:
                    print(f"虚拟机已被删除: {vm_name}，从调度执行表中移除")
                    deleted_vm_indices.append(index)
                    continue
                    
                # 以下处理正在迁移的虚拟机
                if row['status'] == "已发送":
                    target_host = row['recommended_host']
                    start_time = time.strptime(row['start_time'], '%Y-%m-%d %H:%M:%S')
                    start_timestamp = time.mktime(start_time)
                    
                    # 检查是否超时（5分钟）
                    if time.time() - start_timestamp > 300:
                        self.dispatch_execution_df.loc[index, 'status'] = "已超时"
                        self.dispatch_execution_df.loc[index, 'updated_time'] = current_time
                        if not self.is_in_blacklist(vm_name):
                            self.add_to_blacklist(vm_name)
                            print(f"迁移超时: {vm_name} 已添加到黑名单")
                        continue
                    
                    vm_info = vm_info_df.iloc[0]
                    # 检查迁移是否完成
                    if vm_info['task_state'] is None:  # 任务已完成
                        if vm_info['hostname'] == target_host:
                            # 迁移成功
                            self.dispatch_execution_df.loc[index, 'status'] = "已完成"
                            # 更新最后迁移时间
                            self.last_migration_time = time.time()
                            print(f"迁移成功: {vm_name} -> {target_host}")
                        else:
                            # 迁移失败
                            self.dispatch_execution_df.loc[index, 'status'] = "已超时"
                            if not self.is_in_blacklist(vm_name):
                                self.add_to_blacklist(vm_name)
                                print(f"迁移失败: {vm_name} 已添加到黑名单")
                        
                        self.dispatch_execution_df.loc[index, 'updated_time'] = current_time
            except Exception as e:
                print(f"检查虚拟机状态时发生错误: {vm_name}, 错误: {str(e)}")
                # 如果是因为虚拟机不存在导致的错误，也将其从表中删除
                if "not found" in str(e).lower() or "no such" in str(e).lower():
                    print(f"虚拟机不存在: {vm_name}，从调度执行表中移除")
                    deleted_vm_indices.append(index)
        
        # 从调度执行表中删除已被删除的虚拟机记录
        if deleted_vm_indices:
            self.dispatch_execution_df = self.dispatch_execution_df.drop(deleted_vm_indices)
            # 如果删除后表为空，将其设为None
            if self.dispatch_execution_df.empty:
                self.dispatch_execution_df = None
            print(f"已从调度执行表中删除 {len(deleted_vm_indices)} 条已删除虚拟机的记录")

    def openstack_sorted_servers_in_host(self, hostname, addr):
        client = Client()

        servers_usage = self.prometheus_libvirt_usage(hostname, addr)
        servers = client.NovaClient.openstack_get_host_instance_detail(client, hostname)
        merge_lists = self.merge_lists_by_key(servers_usage, servers)

        for server in merge_lists:
            server["cpu_used_percent"] = float('%.3f' % float(server["cpu_used_percent"]))
            server["mem_used_percent"] = float('%.3f' % float(server["mem_used_percent"]))
            server["cpu_used_percent_vm"] = float('%.3f' % float(server["cpu_used_percent_vm"]))
            server["mem_used_rate"] = float('%.3f' % float(server["mem_used_rate"]))
            server["mem_used_bite"] = float('%.3f' % float(server["mem_used_bite"]))

        return merge_lists

    def get_vm_info_df(self, vm_id):
        """获取虚拟机信息并转换为DataFrame"""
        try:
            client = Client()
            servers = client.NovaClient.openstack_get_server_detail(client, vm_id)
            
            # 确保servers是列表形式
            if not isinstance(servers, list):
                servers = [servers]
            
            # 创建DataFrame，指定默认索引
            vm_info_df = pd.DataFrame(servers, index=range(len(servers)))
            
            if vm_info_df.empty:
                print(f"Warning: No data found for VM ID {vm_id}")
                return None
                
            # 确保必要的列存在
            required_columns = ['hostname', 'task_state']
            for col in required_columns:
                if col not in vm_info_df.columns:
                    print(f"Warning: Missing required column '{col}' for VM ID {vm_id}")
                    vm_info_df[col] = None
            
            return vm_info_df
            
        except Exception as e:
            print(f"Error in get_vm_info_df for VM ID {vm_id}: {str(e)}")
            return None

    def merge_lists_by_key(self, list1, list2):
        """合并两个列表通过instance_name键"""
        merged_list = []
        dict1 = {item["instance_name"]: item for item in list1}
        dict2 = {item["instance_name"]: item for item in list2}

        for key, value in dict1.items():
            if key in dict2:
                merged_list.append({**value, **dict2[key]})

        return merged_list

    def prometheus_libvirt_usage(self, hostname, addr):
        """获取libvirt资源使用情况"""
        res = []
        client = PClient()
        params = self.prometheus_time_interval()
        cpu_used_percents = client.HostClient.get_libvirt_domain_cpu_used_percent(client, *params, hostname, addr)
        cpu_used_percents = self.prometheus_dict_to_list(cpu_used_percents)
        cpu_used_percents_vm = client.HostClient.get_libvirt_domain_cpu_used_percent_for_vm(client, *params, hostname, addr)
        cpu_used_percents_vm = self.prometheus_dict_to_list(cpu_used_percents_vm)
        mem_used_percents = client.HostClient.libvirt_domain_memory_stats_usable_bytes(client, *params, addr)
        mem_used_percents = self.prometheus_dict_to_list(mem_used_percents)
        mem_used_rates = client.HostClient.libvirt_domain_memory_rate(client, *params, addr)
        mem_used_rates = self.prometheus_dict_to_list(mem_used_rates)
        mem_used_bites = client.HostClient.libvirt_domain_memory_use_bites(client, *params, addr)
        mem_used_bites = self.prometheus_dict_to_list(mem_used_bites)

        for cpu_used_percent, cpu_used_percent_vm, mem_used_percent, mem_used_rate, mem_used_bite in zip(cpu_used_percents, cpu_used_percents_vm, mem_used_percents, mem_used_rates, mem_used_bites):
            cpu_used_percent["cpu_used_percent"] = cpu_used_percent["value"]
            cpu_used_percent_vm["cpu_used_percent_vm"] = cpu_used_percent_vm["value"]
            mem_used_percent["mem_used_percent"] = mem_used_percent["value"]
            mem_used_rate["mem_used_rate"] = mem_used_rate["value"]
            mem_used_bite["mem_used_bite"] = mem_used_bite["value"] / 1024 / 1024 / 1024 / 100
            cpu_used_percent.pop("value")
            cpu_used_percent_vm.pop("value")
            mem_used_percent.pop("value")
            mem_used_rate.pop("value")
            mem_used_bite.pop("value")
            res.append({**cpu_used_percent, **mem_used_percent, **cpu_used_percent_vm, **mem_used_rate, **mem_used_bite})

        return res

    def prometheus_time_interval(self):
        end = time.time()
        start = end - 60

        return [start, end]

    def prometheus_dict_to_list(self, data):
        res = []
        for row in data["data"]["result"]:
            domain   = row["metric"]["domain"]
            instance = row["metric"]["instance"].split(":")[0]
            values   = [float(value[1]) for value in row["values"]]
            value    = float('%.3f' % (sum(values) / len(values)))
            res.append({"instance_name": domain,
                        "host_ip": instance,
                        "value": value})

        return res
    
    def is_auto_drs_enabled(self):
        """检查是否启用了自动DRS迁移"""
        try:
            if self.drs_df is None or self.drs_df.empty:
                return False
            # 检查活跃配置的auto字段
            active_config = self.drs_df[self.drs_df['enabled'] == "true"].iloc[0]
            return active_config['auto'] == "true"
        except Exception as e:
            print(f"检查自动DRS状态时发生错误: {str(e)}")
            return False

    def auto_drs_migrate(self):
        """自动DRS迁移定时任务"""
        try:
            # 检查是否启用自动DRS
            if not self.is_auto_drs_enabled():
                return
            
            # 检查集群状态
            # if not self.check_cluster_enabled():
            #     return

            # 检查是否在迁移冷却时间内
            if (self.last_migration_time is not None and 
                time.time() - self.last_migration_time < service.MIGRATION_COOLDOWN):
                return

            # 检查是否有正在执行的迁移任务
            if (self.dispatch_execution_df is not None and 
                not self.dispatch_execution_df.empty and 
                (self.dispatch_execution_df['status'] != "准备").any()):
                return

            # 获取推荐的迁移信息
            recommended_data = self.recommended_response_data
            if not recommended_data or recommended_data.get("msg") != "ok":
                return

            # 构造迁移请求
            migration_data = recommended_data["data"]["vm"]
            target_host = recommended_data["data"]["recommended_hosts"][0]
            
            migration_request = type('Request', (), {
                'get_data': lambda self, as_text=True: json.dumps({
                    'id': migration_data['id'],
                    'vm_name': migration_data['vm_name'],
                    'target_host': target_host
                })
            })()

            # 调用迁移方法
            print(f"自动DRS迁移: {migration_data['vm_name']} -> {target_host}")
            self.drs_migrate(migration_request)

        except Exception as e:
            print(f"自动DRS迁移发生错误: {str(e)}")
